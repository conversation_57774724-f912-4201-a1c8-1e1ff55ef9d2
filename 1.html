<!DOCTYPE html>
<!-- saved from url=(0042)file:///Users/<USER>/Documents/sp/index.html# -->
<html lang="zh-CN"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品智算引擎 v0.3 - 智能库存调拨</title>
    <script src="./1_files/saved_resource"></script>
    <link rel="stylesheet" href="./1_files/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-shadow { box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
        .hover-lift { transition: all 0.3s ease; }
        .hover-lift:hover { transform: translateY(-2px); box-shadow: 0 20px 40px -5px rgba(0, 0, 0, 0.15); }
        .pulse-animation { animation: pulse 2s infinite; }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
<style>*, ::before, ::after{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }/* ! tailwindcss v3.4.16 | MIT License | https://tailwindcss.com */*,::after,::before{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}::after,::before{--tw-content:''}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}.fixed{position:fixed}.inset-0{inset:0px}.z-50{z-index:50}.mx-4{margin-left:1rem;margin-right:1rem}.mx-auto{margin-left:auto;margin-right:auto}.mb-1{margin-bottom:0.25rem}.mb-2{margin-bottom:0.5rem}.mb-3{margin-bottom:0.75rem}.mb-4{margin-bottom:1rem}.mb-6{margin-bottom:1.5rem}.mb-8{margin-bottom:2rem}.mr-1{margin-right:0.25rem}.mr-2{margin-right:0.5rem}.mr-3{margin-right:0.75rem}.mt-1{margin-top:0.25rem}.mt-2{margin-top:0.5rem}.mt-6{margin-top:1.5rem}.mt-8{margin-top:2rem}.block{display:block}.flex{display:flex}.grid{display:grid}.hidden{display:none}.h-10{height:2.5rem}.h-12{height:3rem}.h-16{height:4rem}.h-2{height:0.5rem}.h-4{height:1rem}.h-8{height:2rem}.max-h-\[90vh\]{max-height:90vh}.min-h-screen{min-height:100vh}.w-10{width:2.5rem}.w-12{width:3rem}.w-20{width:5rem}.w-4{width:1rem}.w-8{width:2rem}.w-full{width:100%}.max-w-4xl{max-width:56rem}.max-w-7xl{max-width:80rem}.flex-1{flex:1 1 0%}.cursor-pointer{cursor:pointer}.grid-cols-1{grid-template-columns:repeat(1, minmax(0, 1fr))}.grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr))}.items-start{align-items:flex-start}.items-center{align-items:center}.justify-end{justify-content:flex-end}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.gap-4{gap:1rem}.gap-6{gap:1.5rem}.gap-8{gap:2rem}.space-x-2 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(0.5rem * var(--tw-space-x-reverse));margin-left:calc(0.5rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-3 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(0.75rem * var(--tw-space-x-reverse));margin-left:calc(0.75rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-4 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(1rem * var(--tw-space-x-reverse));margin-left:calc(1rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-8 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(2rem * var(--tw-space-x-reverse));margin-left:calc(2rem * calc(1 - var(--tw-space-x-reverse)))}.space-y-2 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(0.5rem * var(--tw-space-y-reverse))}.space-y-3 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(0.75rem * var(--tw-space-y-reverse))}.space-y-4 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(1rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1rem * var(--tw-space-y-reverse))}.space-y-6 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1.5rem * var(--tw-space-y-reverse))}.overflow-y-auto{overflow-y:auto}.rounded-2xl{border-radius:1rem}.rounded-full{border-radius:9999px}.rounded-lg{border-radius:0.5rem}.rounded-xl{border-radius:0.75rem}.border{border-width:1px}.border-2{border-width:2px}.border-b{border-bottom-width:1px}.border-dashed{border-style:dashed}.border-gray-100{--tw-border-opacity:1;border-color:rgb(243 244 246 / var(--tw-border-opacity, 1))}.border-gray-200{--tw-border-opacity:1;border-color:rgb(229 231 235 / var(--tw-border-opacity, 1))}.bg-black{--tw-bg-opacity:1;background-color:rgb(0 0 0 / var(--tw-bg-opacity, 1))}.bg-blue-100{--tw-bg-opacity:1;background-color:rgb(219 234 254 / var(--tw-bg-opacity, 1))}.bg-blue-50{--tw-bg-opacity:1;background-color:rgb(239 246 255 / var(--tw-bg-opacity, 1))}.bg-blue-500{--tw-bg-opacity:1;background-color:rgb(59 130 246 / var(--tw-bg-opacity, 1))}.bg-gray-100{--tw-bg-opacity:1;background-color:rgb(243 244 246 / var(--tw-bg-opacity, 1))}.bg-gray-200{--tw-bg-opacity:1;background-color:rgb(229 231 235 / var(--tw-bg-opacity, 1))}.bg-gray-50{--tw-bg-opacity:1;background-color:rgb(249 250 251 / var(--tw-bg-opacity, 1))}.bg-green-100{--tw-bg-opacity:1;background-color:rgb(220 252 231 / var(--tw-bg-opacity, 1))}.bg-green-50{--tw-bg-opacity:1;background-color:rgb(240 253 244 / var(--tw-bg-opacity, 1))}.bg-green-500{--tw-bg-opacity:1;background-color:rgb(34 197 94 / var(--tw-bg-opacity, 1))}.bg-orange-100{--tw-bg-opacity:1;background-color:rgb(255 237 213 / var(--tw-bg-opacity, 1))}.bg-orange-50{--tw-bg-opacity:1;background-color:rgb(255 247 237 / var(--tw-bg-opacity, 1))}.bg-orange-500{--tw-bg-opacity:1;background-color:rgb(249 115 22 / var(--tw-bg-opacity, 1))}.bg-purple-50{--tw-bg-opacity:1;background-color:rgb(250 245 255 / var(--tw-bg-opacity, 1))}.bg-purple-500{--tw-bg-opacity:1;background-color:rgb(168 85 247 / var(--tw-bg-opacity, 1))}.bg-red-500{--tw-bg-opacity:1;background-color:rgb(239 68 68 / var(--tw-bg-opacity, 1))}.bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.bg-yellow-500{--tw-bg-opacity:1;background-color:rgb(234 179 8 / var(--tw-bg-opacity, 1))}.bg-opacity-50{--tw-bg-opacity:0.5}.bg-gradient-to-r{background-image:linear-gradient(to right, var(--tw-gradient-stops))}.from-blue-500{--tw-gradient-from:#3b82f6 var(--tw-gradient-from-position);--tw-gradient-to:rgb(59 130 246 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.to-purple-600{--tw-gradient-to:#9333ea var(--tw-gradient-to-position)}.p-2{padding:0.5rem}.p-3{padding:0.75rem}.p-4{padding:1rem}.p-6{padding:1.5rem}.p-8{padding:2rem}.px-2{padding-left:0.5rem;padding-right:0.5rem}.px-3{padding-left:0.75rem;padding-right:0.75rem}.px-4{padding-left:1rem;padding-right:1rem}.px-6{padding-left:1.5rem;padding-right:1.5rem}.py-1{padding-top:0.25rem;padding-bottom:0.25rem}.py-2{padding-top:0.5rem;padding-bottom:0.5rem}.py-3{padding-top:0.75rem;padding-bottom:0.75rem}.py-8{padding-top:2rem;padding-bottom:2rem}.text-left{text-align:left}.text-center{text-align:center}.text-2xl{font-size:1.5rem;line-height:2rem}.text-lg{font-size:1.125rem;line-height:1.75rem}.text-sm{font-size:0.875rem;line-height:1.25rem}.text-xl{font-size:1.25rem;line-height:1.75rem}.text-xs{font-size:0.75rem;line-height:1rem}.font-bold{font-weight:700}.font-medium{font-weight:500}.font-semibold{font-weight:600}.text-black{--tw-text-opacity:1;color:rgb(0 0 0 / var(--tw-text-opacity, 1))}.text-blue-500{--tw-text-opacity:1;color:rgb(59 130 246 / var(--tw-text-opacity, 1))}.text-blue-700{--tw-text-opacity:1;color:rgb(29 78 216 / var(--tw-text-opacity, 1))}.text-gray-300{--tw-text-opacity:1;color:rgb(209 213 219 / var(--tw-text-opacity, 1))}.text-gray-400{--tw-text-opacity:1;color:rgb(156 163 175 / var(--tw-text-opacity, 1))}.text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128 / var(--tw-text-opacity, 1))}.text-gray-600{--tw-text-opacity:1;color:rgb(75 85 99 / var(--tw-text-opacity, 1))}.text-green-500{--tw-text-opacity:1;color:rgb(34 197 94 / var(--tw-text-opacity, 1))}.text-green-700{--tw-text-opacity:1;color:rgb(21 128 61 / var(--tw-text-opacity, 1))}.text-orange-500{--tw-text-opacity:1;color:rgb(249 115 22 / var(--tw-text-opacity, 1))}.text-orange-700{--tw-text-opacity:1;color:rgb(194 65 12 / var(--tw-text-opacity, 1))}.text-purple-500{--tw-text-opacity:1;color:rgb(168 85 247 / var(--tw-text-opacity, 1))}.text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.shadow-sm{--tw-shadow:0 1px 2px 0 rgb(0 0 0 / 0.05);--tw-shadow-colored:0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.transition-all{transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.transition-colors{transition-property:color, background-color, border-color, fill, stroke, -webkit-text-decoration-color;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, -webkit-text-decoration-color;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.duration-300{transition-duration:300ms}.hover\:border-blue-300:hover{--tw-border-opacity:1;border-color:rgb(147 197 253 / var(--tw-border-opacity, 1))}.hover\:bg-gray-50:hover{--tw-bg-opacity:1;background-color:rgb(249 250 251 / var(--tw-bg-opacity, 1))}.hover\:text-black:hover{--tw-text-opacity:1;color:rgb(0 0 0 / var(--tw-text-opacity, 1))}.hover\:text-gray-600:hover{--tw-text-opacity:1;color:rgb(75 85 99 / var(--tw-text-opacity, 1))}.hover\:shadow-lg:hover{--tw-shadow:0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.focus\:border-transparent:focus{border-color:transparent}.focus\:ring-2:focus{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)}.focus\:ring-blue-500:focus{--tw-ring-opacity:1;--tw-ring-color:rgb(59 130 246 / var(--tw-ring-opacity, 1))}@media (min-width: 640px){.sm\:px-6{padding-left:1.5rem;padding-right:1.5rem}}@media (min-width: 768px){.md\:flex{display:flex}.md\:grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr))}.md\:grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr))}.md\:grid-cols-4{grid-template-columns:repeat(4, minmax(0, 1fr))}}@media (min-width: 1024px){.lg\:col-span-2{grid-column:span 2 / span 2}.lg\:grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr))}.lg\:grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr))}.lg\:px-8{padding-left:2rem;padding-right:2rem}}</style></head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-4">
                    <div class="w-10 h-10 gradient-bg rounded-xl flex items-center justify-center">
                        <i class="fas fa-brain text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-semibold text-black">商品智算引擎</h1>
                        <span class="text-sm text-gray-500">v0.3</span>
                    </div>
                </div>
                <nav class="hidden md:flex space-x-8">
                    <a href="file:///Users/<USER>/Documents/sp/index.html#" class="text-black hover:text-gray-600 font-medium">仪表盘</a>
                    <a href="file:///Users/<USER>/Documents/sp/index.html#" class="text-black hover:text-gray-600 font-medium">智能调拨</a>
                    <a href="file:///Users/<USER>/Documents/sp/index.html#" class="text-black hover:text-gray-600 font-medium">数据分析</a>
                    <a href="file:///Users/<USER>/Documents/sp/index.html#" class="text-black hover:text-gray-600 font-medium">设置</a>
                </nav>
                <div class="flex items-center space-x-4">
                    <button class="p-2 text-gray-400 hover:text-black rounded-lg">
                        <i class="fas fa-bell"></i>
                    </button>
                    <div class="w-8 h-8 bg-gray-200 rounded-full"></div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Page Title -->
        <div class="mb-8">
            <div class="flex items-center space-x-3 mb-2">
                <div class="w-8 h-8 gradient-bg rounded-lg flex items-center justify-center">
                    <i class="fas fa-exchange-alt text-white text-sm"></i>
                </div>
                <h2 class="text-2xl font-bold text-black">智能库存调拨</h2>
            </div>
            <p class="text-gray-600">基于AI算法的智能库存分配，解决多仓库间库存不均衡问题</p>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-2xl p-6 card-shadow hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500 mb-1">总池位数</p>
                        <p class="text-2xl font-bold text-black">12</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-50 rounded-xl flex items-center justify-center">
                        <i class="fas fa-warehouse text-blue-500"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-2xl p-6 card-shadow hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500 mb-1">待调拨SKU</p>
                        <p class="text-2xl font-bold text-black">1,247</p>
                    </div>
                    <div class="w-12 h-12 bg-green-50 rounded-xl flex items-center justify-center">
                        <i class="fas fa-boxes text-green-500"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-2xl p-6 card-shadow hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500 mb-1">调拨效率</p>
                        <p class="text-2xl font-bold text-black">94.2%</p>
                    </div>
                    <div class="w-12 h-12 bg-purple-50 rounded-xl flex items-center justify-center">
                        <i class="fas fa-chart-line text-purple-500"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-2xl p-6 card-shadow hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500 mb-1">节省成本</p>
                        <p class="text-2xl font-bold text-black">¥28.5万</p>
                    </div>
                    <div class="w-12 h-12 bg-orange-50 rounded-xl flex items-center justify-center">
                        <i class="fas fa-coins text-orange-500"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Dashboard -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Left Column -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Algorithm Control Panel -->
                <div class="bg-white rounded-2xl p-6 card-shadow">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-black">智能调拨控制台</h3>
                        <span class="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm font-medium">
                            <i class="fas fa-circle text-green-500 text-xs mr-1 pulse-animation"></i>
                            算法运行中
                        </span>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-black mb-2">产品类型</label>
                                <select class="w-full p-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option>固口类产品</option>
                                    <option>非固口类产品</option>
                                    <option>混合类型</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-black mb-2">分配策略</label>
                                <select class="w-full p-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option>智能权重分配</option>
                                    <option>按需求比例分配</option>
                                    <option>均衡优先分配</option>
                                </select>
                            </div>
                        </div>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-black mb-2">主权重系数</label>
                                <input type="range" min="0" max="100" value="75" class="w-full">
                                <div class="flex justify-between text-sm text-gray-500 mt-1">
                                    <span>0</span>
                                    <span class="font-medium">75%</span>
                                    <span>100</span>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-black mb-2">二级权重系数</label>
                                <input type="range" min="0" max="100" value="25" class="w-full">
                                <div class="flex justify-between text-sm text-gray-500 mt-1">
                                    <span>0</span>
                                    <span class="font-medium">25%</span>
                                    <span>100</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex space-x-4">
                        <button class="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-6 rounded-xl font-medium hover:shadow-lg transition-all duration-300 hover-lift" style="width: 100%;">
                            <i class="fas fa-play mr-2"></i>
                            开始智能调拨
                        </button>
                        <button class="px-6 py-3 border border-gray-200 text-black rounded-xl font-medium hover:bg-gray-50 transition-colors">
                            <i class="fas fa-download mr-2"></i>
                            导出方案
                        </button>
                    </div>
                </div>

                <!-- Data Import Section -->
                <div class="bg-white rounded-2xl p-6 card-shadow">
                    <h3 class="text-lg font-semibold text-black mb-4">数据导入</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div class="border-2 border-dashed border-gray-200 rounded-xl p-4 text-center hover:border-blue-300 transition-colors cursor-pointer">
                            <i class="fas fa-file-excel text-green-500 text-2xl mb-2"></i>
                            <p class="text-sm font-medium text-black">池位分配需求表</p>
                            <p class="text-xs text-gray-500 mt-1">拖拽或点击上传</p>
                            <div class="mt-2">
                                <span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">已上传</span>
                            </div>
                        </div>
                        <div class="border-2 border-dashed border-gray-200 rounded-xl p-4 text-center hover:border-blue-300 transition-colors cursor-pointer">
                            <i class="fas fa-database text-blue-500 text-2xl mb-2"></i>
                            <p class="text-sm font-medium text-black">共享池可用库存</p>
                            <p class="text-xs text-gray-500 mt-1">拖拽或点击上传</p>
                            <div class="mt-2">
                                <span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">已上传</span>
                            </div>
                        </div>
                        <div class="border-2 border-dashed border-gray-200 rounded-xl p-4 text-center hover:border-blue-300 transition-colors cursor-pointer">
                            <i class="fas fa-warehouse text-purple-500 text-2xl mb-2"></i>
                            <p class="text-sm font-medium text-black">目标池位在库件数</p>
                            <p class="text-xs text-gray-500 mt-1">拖拽或点击上传</p>
                            <div class="mt-2">
                                <span class="px-2 py-1 bg-orange-100 text-orange-700 rounded-full text-xs">待上传</span>
                            </div>
                        </div>
                    </div>

                    <!-- Data Summary -->
                    <div class="bg-gray-50 rounded-xl p-4">
                        <h4 class="text-sm font-medium text-black mb-3">数据概览</h4>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                            <div>
                                <p class="text-lg font-bold text-black">1,247</p>
                                <p class="text-xs text-gray-500">总SKU数</p>
                            </div>
                            <div>
                                <p class="text-lg font-bold text-black">12</p>
                                <p class="text-xs text-gray-500">目标池位</p>
                            </div>
                            <div>
                                <p class="text-lg font-bold text-black">85,432</p>
                                <p class="text-xs text-gray-500">总库存件数</p>
                            </div>
                            <div>
                                <p class="text-lg font-bold text-black">78,901</p>
                                <p class="text-xs text-gray-500">总需求件数</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Inventory Distribution Chart -->
                <div class="bg-white rounded-2xl p-6 card-shadow">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-black">库存分布分析</h3>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 bg-blue-100 text-blue-700 rounded-lg text-sm font-medium">固口类</button>
                            <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-lg text-sm">非固口类</button>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Pool Distribution -->
                        <div>
                            <h4 class="text-sm font-medium text-black mb-4">各池位库存状况</h4>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">华东仓</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-20 bg-gray-200 rounded-full h-2">
                                            <div class="bg-green-500 h-2 rounded-full" style="width: 85%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-black">8,542</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">华南仓</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-20 bg-gray-200 rounded-full h-2">
                                            <div class="bg-blue-500 h-2 rounded-full" style="width: 72%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-black">7,234</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">华北仓</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-20 bg-gray-200 rounded-full h-2">
                                            <div class="bg-yellow-500 h-2 rounded-full" style="width: 45%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-black">4,521</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">西南仓</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-20 bg-gray-200 rounded-full h-2">
                                            <div class="bg-red-500 h-2 rounded-full" style="width: 28%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-black">2,801</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Category Distribution -->
                        <div>
                            <h4 class="text-sm font-medium text-black mb-4">产品类别分布</h4>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-4 h-4 bg-blue-500 rounded-full"></div>
                                        <span class="text-sm text-gray-600">手镯类</span>
                                    </div>
                                    <span class="text-sm font-medium text-black">35.2%</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-4 h-4 bg-green-500 rounded-full"></div>
                                        <span class="text-sm text-gray-600">戒指类</span>
                                    </div>
                                    <span class="text-sm font-medium text-black">28.7%</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-4 h-4 bg-purple-500 rounded-full"></div>
                                        <span class="text-sm text-gray-600">项链类</span>
                                    </div>
                                    <span class="text-sm font-medium text-black">22.1%</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-4 h-4 bg-orange-500 rounded-full"></div>
                                        <span class="text-sm text-gray-600">其他类</span>
                                    </div>
                                    <span class="text-sm font-medium text-black">14.0%</span>
                                </div>
                            </div>

                            <div class="mt-6 p-4 bg-blue-50 rounded-xl">
                                <div class="flex items-center space-x-2 mb-2">
                                    <i class="fas fa-lightbulb text-blue-500"></i>
                                    <span class="text-sm font-medium text-black">智能建议</span>
                                </div>
                                <p class="text-xs text-gray-600">
                                    西南仓库存严重不足，建议优先从华东仓调拨手镯类产品156件，预计可提升该区域销售效率23%
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="space-y-6">
                <!-- Algorithm Status -->
                <div class="bg-white rounded-2xl p-6 card-shadow">
                    <h3 class="text-lg font-semibold text-black mb-4">算法状态</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">数据预处理</span>
                            <span class="text-green-500">100%</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">权重计算</span>
                            <span class="text-green-500"><i class="fas fa-check-circle"></i></span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">智能分配</span>
                            <span class="text-blue-500 pulse-animation"><i class="fas fa-spinner fa-spin"></i></span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">结果优化</span>
                            <span class="text-gray-300"><i class="fas fa-clock"></i></span>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <div class="flex justify-between text-sm text-gray-600 mb-2">
                            <span>处理进度</span>
                            <span>67%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full" style="width: 67%"></div>
                        </div>
                    </div>
                </div>

                <!-- Recent Results -->
                <div class="bg-white rounded-2xl p-6 card-shadow">
                    <h3 class="text-lg font-semibold text-black mb-4">最近调拨结果</h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                            <div>
                                <p class="text-sm font-medium text-black">华东仓 → 华南仓</p>
                                <p class="text-xs text-gray-500">手镯类 · 156件</p>
                            </div>
                            <span class="text-green-500 text-sm font-medium">完成</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                            <div>
                                <p class="text-sm font-medium text-black">华北仓 → 西南仓</p>
                                <p class="text-xs text-gray-500">戒指类 · 89件</p>
                            </div>
                            <span class="text-blue-500 text-sm font-medium">进行中</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                            <div>
                                <p class="text-sm font-medium text-black">华中仓 → 华东仓</p>
                                <p class="text-xs text-gray-500">项链类 · 234件</p>
                            </div>
                            <span class="text-orange-500 text-sm font-medium">待确认</span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-2xl p-6 card-shadow">
                    <h3 class="text-lg font-semibold text-black mb-4">快速操作</h3>
                    <div class="space-y-3">
                        <button class="w-full text-left p-3 hover:bg-gray-50 rounded-xl transition-colors">
                            <i class="fas fa-file-download text-blue-500 mr-3"></i>
                            <span class="text-sm font-medium text-black">下载调拨报告</span>
                        </button>
                        <button class="w-full text-left p-3 hover:bg-gray-50 rounded-xl transition-colors">
                            <i class="fas fa-chart-bar text-green-500 mr-3"></i>
                            <span class="text-sm font-medium text-black">查看分析报表</span>
                        </button>
                        <button class="w-full text-left p-3 hover:bg-gray-50 rounded-xl transition-colors">
                            <i class="fas fa-cog text-purple-500 mr-3"></i>
                            <span class="text-sm font-medium text-black">算法参数设置</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Algorithm Details Modal (Hidden by default) -->
    <div id="algorithmModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-2xl p-8 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-bold text-black">智能调拨算法详情</h3>
                <button onclick="closeModal()" class="text-gray-400 hover:text-black">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Algorithm Flow -->
                <div>
                    <h4 class="text-lg font-semibold text-black mb-4">算法流程</h4>
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                            <div>
                                <h5 class="font-medium text-black">产品分类识别</h5>
                                <p class="text-sm text-gray-600">自动识别固口类与非固口类产品，应用不同处理策略</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                            <div>
                                <h5 class="font-medium text-black">库存状况评估</h5>
                                <p class="text-sm text-gray-600">分析总库存与总需求，判断充足或不足情况</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                            <div>
                                <h5 class="font-medium text-black">智能权重计算</h5>
                                <p class="text-sm text-gray-600">主权重优先库存少的池位，二级权重平衡SKU分配</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold">4</div>
                            <div>
                                <h5 class="font-medium text-black">随机化分配</h5>
                                <p class="text-sm text-gray-600">防止SKU偏向性操控，确保分配公平性</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center text-sm font-bold">5</div>
                            <div>
                                <h5 class="font-medium text-black">结果优化输出</h5>
                                <p class="text-sm text-gray-600">生成各池位独立分配文件，支持Excel导出</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Algorithm Parameters -->
                <div>
                    <h4 class="text-lg font-semibold text-black mb-4">核心参数</h4>
                    <div class="space-y-4">
                        <div class="bg-gray-50 rounded-xl p-4">
                            <h5 class="font-medium text-black mb-2">主权重系数 (α)</h5>
                            <p class="text-sm text-gray-600 mb-3">控制库存均衡的优先级，值越大越倾向于给库存少的池位分配更多商品</p>
                            <div class="flex items-center space-x-3">
                                <span class="text-sm text-gray-500">0.5</span>
                                <div class="flex-1 bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-500 h-2 rounded-full" style="width: 75%"></div>
                                </div>
                                <span class="text-sm text-gray-500">1.0</span>
                                <span class="text-sm font-medium text-black">0.75</span>
                            </div>
                        </div>

                        <div class="bg-gray-50 rounded-xl p-4">
                            <h5 class="font-medium text-black mb-2">二级权重系数 (β)</h5>
                            <p class="text-sm text-gray-600 mb-3">平衡SKU分配效率，确保库存多的SKU能够优先分配</p>
                            <div class="flex items-center space-x-3">
                                <span class="text-sm text-gray-500">0.1</span>
                                <div class="flex-1 bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-500 h-2 rounded-full" style="width: 50%"></div>
                                </div>
                                <span class="text-sm text-gray-500">0.5</span>
                                <span class="text-sm font-medium text-black">0.25</span>
                            </div>
                        </div>

                        <div class="bg-gray-50 rounded-xl p-4">
                            <h5 class="font-medium text-black mb-2">随机化因子 (γ)</h5>
                            <p class="text-sm text-gray-600 mb-3">防止算法偏向性，增加分配结果的随机性和公平性</p>
                            <div class="flex items-center space-x-3">
                                <span class="text-sm text-gray-500">0.0</span>
                                <div class="flex-1 bg-gray-200 rounded-full h-2">
                                    <div class="bg-purple-500 h-2 rounded-full" style="width: 30%"></div>
                                </div>
                                <span class="text-sm text-gray-500">0.3</span>
                                <span class="text-sm font-medium text-black">0.1</span>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6">
                        <h5 class="font-medium text-black mb-3">算法优势</h5>
                        <div class="space-y-2">
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500"></i>
                                <span class="text-sm text-gray-600">随机性防止SKU偏向操控</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500"></i>
                                <span class="text-sm text-gray-600">可控性确保不超总库存</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500"></i>
                                <span class="text-sm text-gray-600">灵活输出支持多格式导出</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500"></i>
                                <span class="text-sm text-gray-600">参数可调适应不同业务</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-8 flex justify-end space-x-4">
                <button onclick="closeModal()" class="px-6 py-2 border border-gray-200 text-black rounded-xl hover:bg-gray-50">
                    关闭
                </button>
                <button class="px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:shadow-lg">
                    应用设置
                </button>
            </div>
        </div>
    </div>

    <script>
        function openModal() {
            document.getElementById('algorithmModal').classList.remove('hidden');
            document.getElementById('algorithmModal').classList.add('flex');
        }

        function closeModal() {
            document.getElementById('algorithmModal').classList.add('hidden');
            document.getElementById('algorithmModal').classList.remove('flex');
        }

        // Add click event to algorithm details button
        document.addEventListener('DOMContentLoaded', function() {
            // Add modal trigger to algorithm settings button
            const settingsButtons = document.querySelectorAll('button');
            settingsButtons.forEach(button => {
                if (button.textContent.includes('算法参数设置')) {
                    button.addEventListener('click', openModal);
                }
            });

            // Simulate real-time updates
            setInterval(() => {
                const progressBar = document.querySelector('.bg-gradient-to-r.from-blue-500.to-purple-600');
                if (progressBar) {
                    const currentWidth = parseInt(progressBar.style.width) || 67;
                    const newWidth = Math.min(currentWidth + Math.random() * 2, 100);
                    progressBar.style.width = newWidth + '%';

                    const progressText = document.querySelector('.text-sm.text-gray-600 + span');
                    if (progressText) {
                        progressText.textContent = Math.round(newWidth) + '%';
                    }
                }
            }, 3000);
        });
    </script>


</body></html>